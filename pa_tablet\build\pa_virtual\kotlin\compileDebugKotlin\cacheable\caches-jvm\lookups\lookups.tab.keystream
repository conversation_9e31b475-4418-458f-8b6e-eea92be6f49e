  RELEASE android.os.Build.VERSION  NonNull androidx.annotation  
FlutterPlugin 3br.com.unimedfortaleza.mobile.components.pa_virtual  
MethodCall 3br.com.unimedfortaleza.mobile.components.pa_virtual  MethodCallHandler 3br.com.unimedfortaleza.mobile.components.pa_virtual  
MethodChannel 3br.com.unimedfortaleza.mobile.components.pa_virtual  NonNull 3br.com.unimedfortaleza.mobile.components.pa_virtual  PaVirtualPlugin 3br.com.unimedfortaleza.mobile.components.pa_virtual  Result 3br.com.unimedfortaleza.mobile.components.pa_virtual  android 3br.com.unimedfortaleza.mobile.components.pa_virtual  FlutterPluginBinding Abr.com.unimedfortaleza.mobile.components.pa_virtual.FlutterPlugin  
MethodChannel Cbr.com.unimedfortaleza.mobile.components.pa_virtual.PaVirtualPlugin  android Cbr.com.unimedfortaleza.mobile.components.pa_virtual.PaVirtualPlugin  channel Cbr.com.unimedfortaleza.mobile.components.pa_virtual.PaVirtualPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               