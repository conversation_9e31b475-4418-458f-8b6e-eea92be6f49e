"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\pa-tablet\\pa_tablet\\build\\twilio_programmable_video\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c39ce26f1d81bf1591d74e9af2155719\\transformed\\jetified-viewbinding-8.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d97779dffc04b561b00c837895723113\\transformed\\jetified-flutter_embedding_debug-1.0.0-dd93de6fb1776398bf586cbd477deade1391c7e4.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2f03ed3d264e3699809be96229379276\\transformed\\jetified-kotlin-parcelize-runtime-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\580f5e6caefb7a3506011fad95d270cc\\transformed\\jetified-firebase-iid-21.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2e3b05f362b3c5302ea8f17c6bda7b0b\\transformed\\jetified-play-services-cloud-messaging-16.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5dddbe78b962b77b26c00e26499398a3\\transformed\\jetified-play-services-stats-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\67308a7103c323d251a4921d0f185c2f\\transformed\\jetified-firebase-installations-16.3.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93359b15199321405cbf140ba5f037d8\\transformed\\jetified-firebase-common-19.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5f2185b674d1597a1633b02b9c65e487\\transformed\\jetified-firebase-iid-interop-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1d0fb8080b789eeaddef763f865e9094\\transformed\\jetified-firebase-installations-interop-16.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6665906baae90a2884043b0cc9bda637\\transformed\\jetified-play-services-tasks-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\258820317fa81e772d4aeece634180db\\transformed\\jetified-play-services-basement-17.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5b20fa435bf1620d807b08ec7cb73dd5\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fbf942be408b2a5ea1f9e17cb3faa0d\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\64a446ad9066d997553a8c00638d1a3a\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f038ac3588aca66230f14b4b160725df\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cfe63d2d4f3c8d3bbf1b53197cd561ed\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c443681db5575b21e9e202404602271b\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\17d35f7fb615e9185d8c7fbb2e3e04ee\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\81b8d4adeb72b905dee32c798efad7f3\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\333888a9c9774b683603627998641675\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6520dd03cd2767cfb47ff991da088b42\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09f4cdc27fe5c16d7d1630887fdd8c58\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\07bea844a94fd3f463a32063cf241173\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a068cfba841cce725a8149b0e96ffdd4\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0122129eef99944455e1a6e802400880\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\88a9c7bdbfb0b2f31263450aa6ffeba0\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3b3d2242ea0d82e227d56d60cd11d3e0\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6bfbaeb94bafb2d7ca266198cf56b55\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\25af8473384b43a3c1f34f18dbaf8ac4\\transformed\\jetified-kotlin-android-extensions-runtime-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e2b3cacf5f9715102a2345854c5ab23\\transformed\\jetified-video-android-7.6.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7230d3a721a08e77e9de5e55bd38f8a1\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\97395ed50e42cc46a9d71cebd8e47af2\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ce7241d18c2a8ffe3f0ca3952ea04209\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\59b4a77dcd987fd925dc80c449c6cc96\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\064c376cdb2ecbe8bef9d4289d9485be\\transformed\\jetified-firebase-components-16.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\10bfcadef974a75019f12905b75227af\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae0187ead4b3d4d95045f7ca8b8568a3\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bcb58a2b74d1424678b01a21f1302755\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f796d9c4684a07530b93eaf2a2137b16\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\572b69c0c0d31ba5ccb08d3a93f2485c\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b3b99d844e6f1f8481be3a2ec36ab90a\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b3f9f909c8019a23a3fa60c1bdaacf9e\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\19722821f031208e702edb09560fa9b8\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f4ee4ed1c1c08e26f9f62a2c96ba1ca\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3ce23e0ec218f9f5588d0690a7ba1552\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\60cd96cb76abbd5cc52f61e0f1c2abff\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1703ec764d807374fb6f51e4f000e3ff\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\925358f5a048329074206a0d8a0c98cd\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c84d325b8fa3dfd264baf0d930c059bf\\transformed\\jetified-firebase-annotations-16.0.0.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-31\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\pa-tablet\\pa_tablet\\build\\twilio_programmable_video\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "twilio_programmable_video_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "-Xplugin=C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-parcelize-compiler\\2.1.0\\f02e0c3d2c752b93cacd757cc1c1176ce7a129a0\\kotlin-parcelize-compiler-2.1.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-compiler-embeddable\\2.1.0\\988bf980588bbbcfa1ba19c1ffd7aa869b335a31\\kotlin-compiler-embeddable-2.1.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.1.0\\85f8b81009cda5890e54ba67d64b5e599c645020\\kotlin-stdlib-2.1.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-script-runtime\\2.1.0\\5909da8c57b75a117714ab0e348c86101b7a3284\\kotlin-script-runtime-2.1.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-reflect\\1.6.10\\1cbe9c92c12a94eea200d23c2bbaedaf3daf5132\\kotlin-reflect-1.6.10.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-daemon-embeddable\\2.1.0\\420bae1908e4a353f5b56c3c850d24d86367b25d\\kotlin-daemon-embeddable-2.1.0.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.intellij.deps\\trove4j\\1.0.20200330\\3afb14d5f9ceb459d724e907a21145e8ff394f02\\trove4j-1.0.20200330.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.6.4\\2c997cd1c0ef33f3e751d3831929aeff1390cb30\\kotlinx-coroutines-core-jvm-1.6.4.jar,C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\13.0\\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\\annotations-13.0.jar" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\AudioNotificationListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\AudioSettings.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\BaseListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\LocalParticipantListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\ParticipantView.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\ParticipantViewFactory.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\PluginHandler.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\RemoteDataTrackListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\RemoteParticipantListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\RoomListener.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\StatsMapper.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\TwilioProgrammableVideoPlugin.kt" "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\flutter_packages\\twilio_programmable_video\\android\\src\\main\\kotlin\\twilio\\flutter\\twilio_programmable_video\\VideoCapturerHandler.kt"