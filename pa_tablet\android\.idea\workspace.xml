<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="901c5bb5-0d77-45e6-855d-a7b3805d295a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/evaluation/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/evaluation/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/android/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/android/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/example/.flutter-plugins-dependencies" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/example/.flutter-plugins-dependencies" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/example/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/example/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/pa_virtual/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/remote_log_elastic/.flutter-plugins-dependencies" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/remote_log_elastic/.flutter-plugins-dependencies" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/remote_log_elastic/lib/remote_log_elastic.dart" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/remote_log_elastic/lib/remote_log_elastic.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/splash_unimed/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/splash_unimed/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/android/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/android/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/ephemeral/.plugin_symlinks/device_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/ephemeral/.plugin_symlinks/package_info_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/ephemeral/.plugin_symlinks/path_provider_linux" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/ephemeral/.plugin_symlinks/teleconsulta_unimed" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/ephemeral/.plugin_symlinks/wakelock_plus" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/generated_plugins.cmake" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/linux/flutter/generated_plugins.cmake" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/example/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/teleconsulta_unimed/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/twilio_programmable_video/android/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/twilio_programmable_video/android/build.gradle" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../flutter_packages/twilio_programmable_video/example/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/../../../flutter_packages/twilio_programmable_video/example/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../.metadata" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../analysis_options.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/google-services.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/proguard-rules.pro" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/debug/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/ic_launcher-playstore.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/kotlin/br/com/unimedfortaleza/pavirtual_tablet/MainActivity.kt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/drawable-v21/launch_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/drawable/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/drawable/launch_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-hdpi/ic_launcher_foreground.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xhdpi/ic_launcher_foreground.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xxhdpi/ic_launcher_foreground.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_foreground.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/values-night/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/values/ic_launcher_background.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/main/res/values/styles.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/app/src/profile/AndroidManifest.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/build.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/gradle.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/gradle/wrapper/gradle-wrapper.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../android/settings.gradle" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/animations/orange-ripple.flr" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/animations/unimed_logo.flr" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedIcons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedSans-Regular.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedSans-RegularItalic.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedSans-SemiBold.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedSans-SemiBoldItalic.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/UnimedSlab-Bold.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/conecta-saude/ConectaSaudeIcons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/fonts/unimed-slab/UnimedSlab-Regular.otf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/i18n/pt_BR.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/icomoon/fonts/uicons.ttf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/icon/icone-pa-tablet.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/agendamento-home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/autorizacoes-home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/body.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/faturas-home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/action_button_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/angle_to_left_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/angle_to_left_down_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/angle_to_right_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/angle_to_right_down_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/icon_step_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/numeric_step_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/feature_discovery/ve_service/to_right_arrow.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/guia-medico-home.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/login-background.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/logo-minha-unimed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/logo-minha-unimed.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/logo-somo-coop.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/logo-unimed.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/logo-unimed.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/ambulance.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/clinica_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/clinica_unimed_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/hospital_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/lab_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/localizacao_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/medico_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/person.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/pin_maps/sangue_pin.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/qrcode_teste.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-assustada.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-bracos-cruzados.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-chat.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-duvida.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-esperando.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-raiva.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-sorrindo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-tchau.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta-triste.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/roberta.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../assets/images/virtual-card-check.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../bin/build_android_prod.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../bin/firebase_upload_android.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../bin/prod_firebase_upload_android.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Flutter/AppFrameworkInfo.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Flutter/Debug.xcconfig" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Flutter/Release.xcconfig" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Podfile" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcodeproj/project.pbxproj" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcodeproj/project.xcworkspace/contents.xcworkspacedata" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcodeproj/project.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcodeproj/project.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcworkspace/contents.xcworkspacedata" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcworkspace/xcshareddata/IDEWorkspaceChecks.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/AppDelegate.swift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/AppIcon.appiconset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/LaunchImage.imageset/LaunchImage.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/LaunchImage.imageset/<EMAIL>" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Base.lproj/LaunchScreen.storyboard" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Base.lproj/Main.storyboard" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Info.plist" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../ios/Runner/Runner-Bridging-Header.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/auth/auth_bloc.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/auth/auth_event.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/auth/auth_state.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/unity/unity_bloc.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/unity/unity_event.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/bloc/unity/unity_state.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/blocs_load.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/firebase_options.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/main.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/main_prod.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/model/pa_virtual_login.vo.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/model/room_model.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/model/unit_model.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/screen/main.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/screen/select_card_screen.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/api/auth_api.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/api/config_api.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/colors.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/exceptions.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/flavor_banner.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/flavor_config.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/http_client.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/locator.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/logger_print.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/twilio_service.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/utils/http.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/utils/messages.exceptions.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/utils/pa_virtual_utils.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/utils/string_utils.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/utils/validators.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/version.service.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/alert.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/alert_password.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/styles.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/circle_button.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/clipped_video.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/conference_button_bar.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/conference_page.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/conference_room.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/draggable_publisher.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/noise_box.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/participant.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../lib/shared/widget/twilio/plataform_dialog.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../pubspec.lock" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../pubspec.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../test/shared/api/auth_api_test.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../test/widget_test.dart" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/bin/dart" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/bin/dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/bin/flutter" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/bin/flutter" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/bin/flutter-dev" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/bin/flutter-dev" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/bin/internal/update_dart_sdk.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/bin/internal/update_dart_sdk.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/bin/internal/update_engine_version.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/bin/internal/update_engine_version.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/bots/codelabs_build_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/bots/codelabs_build_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/bots/docs.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/bots/docs.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/bin/conductor" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/bin/conductor" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/bin/packages_autoroller" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/bin/packages_autoroller" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/core/lib/src/proto/compile_proto.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/conductor/core/lib/src/proto/compile_proto.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/customer_testing/ci.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/customer_testing/ci.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/deferred_components_test/download_assets.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/deferred_components_test/download_assets.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/deferred_components_test/run_release_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/deferred_components_test/run_release_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/flutter_gallery/tool/run_instrumentation_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/flutter_gallery/tool/run_instrumentation_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/ios_add2app_life_cycle/build_and_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/integration_tests/ios_add2app_life_cycle/build_and_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/format.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/format.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/gen_keycodes/bin/gen_keycodes" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/gen_keycodes/bin/gen_keycodes" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/repackage_gradle_wrapper.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/repackage_gradle_wrapper.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/test/mock_git.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/dev/tools/test/mock_git.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/android/gyp/jar.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/android/gyp/jar.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/android/gyp/javac.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/android/gyp/javac.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/clobber.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/clobber.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/compiler_version.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/compiler_version.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/dir_exists.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/dir_exists.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/git-hooks/pre-commit" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/git-hooks/pre-commit" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/install-chromeos-fonts.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/install-chromeos-fonts.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/pkg-config-wrapper" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/pkg-config-wrapper" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/rewrite_dirs.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/rewrite_dirs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/sysroot_ld_path.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/sysroot_ld_path.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/sysroot_scripts/install-sysroot.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/linux/sysroot_scripts/install-sysroot.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/ls.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/ls.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/change_mach_o_flags.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/change_mach_o_flags.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/change_mach_o_flags_from_xcode.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/change_mach_o_flags_from_xcode.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/copy_asan_runtime_dylib.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/copy_asan_runtime_dylib.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/copy_framework_unversioned.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/copy_framework_unversioned.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/edit_xibs.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/edit_xibs.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/find_sdk.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/find_sdk.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/make_more_helpers.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/make_more_helpers.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/strip_from_xcode" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/strip_from_xcode" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/strip_save_dsym" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/strip_save_dsym" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/tweak_info_plist.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/tweak_info_plist.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/verify_no_objc.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/mac/verify_no_objc.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/toolchain/clang_static_analyzer_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/build/toolchain/clang_static_analyzer_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/bin/et" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/bin/et" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/dart/tools/dart_pkg.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/dart/tools/dart_pkg.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/generate_coverage.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/generate_coverage.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/git_revision.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/git_revision.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/install-build-deps-linux-desktop.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/install-build-deps-linux-desktop.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/secondary/third_party/protobuf/protoc_wrapper.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/secondary/third_party/protobuf/protoc_wrapper.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/zip.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/build/zip.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/analyze.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/analyze.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/ban_generated_plugin_registrant_java.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/ban_generated_plugin_registrant_java.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/binary_size_treemap.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/binary_size_treemap.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/check_build_configs.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/check_build_configs.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/clang_tidy.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/clang_tidy.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/format.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/format.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/licenses.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/licenses.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/pylint.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/pylint.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/test/ban_generated_plugin_registrant_java_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/ci/test/ban_generated_plugin_registrant_java_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/glfw/run.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/glfw/run.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/glfw_drm/run.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/glfw_drm/run.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/vulkan_glfw/run.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/examples/vulkan_glfw/run.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/impeller/tools/malioc_cores.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/impeller/tools/malioc_cores.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/impeller/tools/malioc_diff.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/impeller/tools/malioc_diff.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/lib/web_ui/dev/felt" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/lib/web_ui/dev/felt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/lib/web_ui/dev/web_engine_analysis.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/lib/web_ui/dev/web_engine_analysis.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/darwin/find-undocumented-ios.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/darwin/find-undocumented-ios.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/fuchsia/flutter/build/asset_package.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/fuchsia/flutter/build/asset_package.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/fuchsia/flutter/build/gen_debug_wrapper_main.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/shell/platform/fuchsia/flutter/build/gen_debug_wrapper_main.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/cp.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/cp.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_embedder_framework.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_embedder_framework.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_binary.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_binary.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_framework.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_framework.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_gen_snapshots.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_macos_gen_snapshots.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_xcframework.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/create_xcframework.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/dist_dart_pkg.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/dist_dart_pkg.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/flutter_gdb" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/flutter_gdb" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/install_framework_headers.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/sky/tools/install_framework_headers.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/analyze_core_dump.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/analyze_core_dump.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/android_systrace_test.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/android_systrace_test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/benchmark/displaylist_benchmark_parser.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/benchmark/displaylist_benchmark_parser.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/dart/run_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/dart/run_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/fuchsia/run_tests.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/fuchsia/run_tests.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/fuchsia/run_tests_test.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/fuchsia/run_tests_test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/ios_scenario_app/run_ios_tests.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/ios_scenario_app/run_ios_tests.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/run_tests.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/run_tests.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/run_tests.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/run_tests.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/sanitizer_suppressions.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/testing/sanitizer_suppressions.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/android_sdk/create_cipd_packages.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/android_sdk/create_cipd_packages.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/cipd/malioc/generate.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/cipd/malioc/generate.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/download_fuchsia_sdk.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/download_fuchsia_sdk.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/engine_roll_pr_desc.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/engine_roll_pr_desc.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/find_pubspecs_to_workspacify.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/find_pubspecs_to_workspacify.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/font_subset/test.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/font_subset/test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/build_fuchsia_artifacts.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/build_fuchsia_artifacts.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/build_fuchsia_artifacts_test.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/build_fuchsia_artifacts_test.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/copy_debug_symbols.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/copy_debug_symbols.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/gen_app_invocation.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/gen_app_invocation.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/gen_dart_package_config.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/gen_dart_package_config.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/kernel/convert_manifest_to_json.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/kernel/convert_manifest_to_json.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/merge_deps_sources.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/merge_deps_sources.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/verify_sources.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/dart/verify_sources.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/depfile_path_to_relative.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/depfile_path_to_relative.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/branch_from_fuchsia.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/branch_from_fuchsia.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/build_and_copy_to_fuchsia.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/build_and_copy_to_fuchsia.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/checkout_fuchsia_revision.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/checkout_fuchsia_revision.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/run_integration_test.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/run_integration_test.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/run_unit_tests.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/run_unit_tests.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/test/test_build_and_copy_to_fuchsia.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/test/test_build_and_copy_to_fuchsia.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/test/test_run_unit_tests.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/devshell/test/test_run_unit_tests.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/interpolate_test_suite.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/interpolate_test_suite.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/make_build_info.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/make_build_info.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/merge_and_upload_debug_symbols.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/merge_and_upload_debug_symbols.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/parse_manifest.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/parse_manifest.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/toolchain/copy.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/toolchain/copy.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/upload_to_symbol_server.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/fuchsia/upload_to_symbol_server.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gen_docs.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gen_docs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gen_test_font.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gen_test_font.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/post-checkout" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/post-checkout" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/post-merge" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/post-merge" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/pre-push" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/pre-push" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/pre-rebase" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/pre-rebase" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/setup.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/githooks/setup.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gn" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/gn" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/javadoc/gen_javadoc.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/javadoc/gen_javadoc.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/luci/build.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/luci/build.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/vscode_workspace/merge.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/vscode_workspace/merge.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/vscode_workspace/refresh.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/vscode_workspace/refresh.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/yapf.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/flutter/tools/yapf.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/tools/dart/create_updated_flutter_deps.py" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/engine/src/tools/dart/create_updated_flutter_deps.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/macos_assemble.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/macos_assemble.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/tool_backend.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/tool_backend.sh" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/xcode_backend.sh" beforeDir="false" afterPath="$PROJECT_DIR$/../../../../../../../../flutter/packages/flutter_tools/bin/xcode_backend.sh" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=LocalEmulator, isTemplate=false, identifier=path=C:\Users\<USER>\.android\avd\Pixel_Tablet.avd)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2z5aAIet2MJf7VzDHJVuuqhXy6S" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feature/upgrade-flutter-pa-tablet&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;AndroidSdkUpdater&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="android.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="901c5bb5-0d77-45e6-855d-a7b3805d295a" name="Changes" comment="" />
      <created>1751023244708</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751023244708</updated>
    </task>
    <servers />
  </component>
</project>