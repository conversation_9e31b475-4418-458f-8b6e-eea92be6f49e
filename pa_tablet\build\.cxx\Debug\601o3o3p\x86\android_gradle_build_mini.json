{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\pa-tablet\\pa_tablet\\build\\.cxx\\Debug\\601o3o3p\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Documentos\\unimed\\pa-tablet\\pa_tablet\\build\\.cxx\\Debug\\601o3o3p\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}