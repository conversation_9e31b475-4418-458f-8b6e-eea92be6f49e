import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:pa_tablet/bloc/auth/auth_bloc.dart';
import 'package:pa_tablet/bloc/unity/unity_bloc.dart';

class BlocsLoad extends StatelessWidget {
  final Widget child;

  const BlocsLoad({Key? key, required this.child}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(create: (context) => AuthBloc()),
        BlocProvider<UnityBloc>(create: (context) => UnityBloc()),
        //...initBlocLoad(context),
      ],
      child: child,
    );
  }
}
