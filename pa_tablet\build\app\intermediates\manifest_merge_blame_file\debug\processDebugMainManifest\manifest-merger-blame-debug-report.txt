1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="br.com.unimedfortaleza.pa_tablet"
4    android:versionCode="201003"
5    android:versionName="2.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
31-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-74
32    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
32-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
32-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-78
33    <uses-permission android:name="android.permission.WAKE_LOCK" />
33-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
33-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
34    <uses-permission android:name="android.permission.VIBRATE" />
34-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
34-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.CAMERA" />
36-->[:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
36-->[:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-62
37    <uses-permission android:name="android.permission.RECORD_AUDIO" />
37-->[:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
37-->[:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
38    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
38-->[:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
38-->[:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
39
40    <permission
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
41        android:name="br.com.unimedfortaleza.pa_tablet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="br.com.unimedfortaleza.pa_tablet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
45    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
45-->[com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:8:5-80
45-->[com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:8:22-77
46    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
46-->[com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:10:5-76
46-->[com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:10:22-73
47
48    <application
49        android:name="android.app.Application"
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:icon="@mipmap/ic_launcher"
54        android:label="pa_tablet"
55        android:testOnly="true" >
56        <activity
57            android:name="br.com.unimedfortaleza.pa_tablet.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:taskAffinity=""
63            android:theme="@style/LaunchTheme"
64            android:windowSoftInputMode="adjustResize" >
65
66            <!--
67                 Specifies an Android theme to apply to this Activity as soon as
68                 the Android process has started. This theme is visible to the user
69                 while the Flutter UI initializes. After that, this theme continues
70                 to determine the Window background behind the Flutter UI.
71            -->
72            <meta-data
73                android:name="io.flutter.embedding.android.NormalTheme"
74                android:resource="@style/NormalTheme" />
75
76            <intent-filter>
77                <action android:name="android.intent.action.MAIN" />
78
79                <category android:name="android.intent.category.LAUNCHER" />
80            </intent-filter>
81        </activity>
82        <!--
83             Don't delete the meta-data below.
84             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
85        -->
86        <meta-data
87            android:name="flutterEmbedding"
88            android:value="2" />
89
90        <service
90-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:44
91            android:name="id.flutter.flutter_background_service.BackgroundService"
91-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-83
92            android:enabled="true"
92-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-35
93            android:exported="true"
93-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-36
94            android:stopWithTask="false" />
94-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-41
95
96        <receiver
96-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-21:39
97            android:name="id.flutter.flutter_background_service.WatchdogReceiver"
97-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-82
98            android:enabled="true"
98-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-35
99            android:exported="true" />
99-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
100        <receiver
100-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-31:20
101            android:name="id.flutter.flutter_background_service.BootReceiver"
101-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-78
102            android:enabled="true"
102-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-35
103            android:exported="true" >
103-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
104            <intent-filter>
104-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-30:29
105                <action android:name="android.intent.action.BOOT_COMPLETED" />
105-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-79
105-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-76
106                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
106-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
106-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
107                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
107-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
107-->[:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
108            </intent-filter>
109        </receiver>
110
111        <provider
111-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
112            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
112-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
113            android:authorities="br.com.unimedfortaleza.pa_tablet.flutter.image_provider"
113-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
114            android:exported="false"
114-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
115            android:grantUriPermissions="true" >
115-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
116            <meta-data
116-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
117                android:name="android.support.FILE_PROVIDER_PATHS"
117-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
118                android:resource="@xml/flutter_image_picker_file_paths" />
118-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
119        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
120        <service
120-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
121            android:name="com.google.android.gms.metadata.ModuleDependencies"
121-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
122            android:enabled="false"
122-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
123            android:exported="false" >
123-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
124            <intent-filter>
124-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
125                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
125-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
125-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
126            </intent-filter>
127
128            <meta-data
128-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
129                android:name="photopicker_activity:0:required"
129-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
130                android:value="" />
130-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
131        </service>
132
133        <provider
133-->[:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
134            android:name="io.flutter.plugins.share.ShareFileProvider"
134-->[:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-70
135            android:authorities="br.com.unimedfortaleza.pa_tablet.flutter.share_provider"
135-->[:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-74
136            android:exported="false"
136-->[:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
137            android:grantUriPermissions="true" >
137-->[:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
138            <meta-data
138-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
139                android:name="android.support.FILE_PROVIDER_PATHS"
139-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
140                android:resource="@xml/flutter_share_file_paths" />
140-->[:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
141        </provider>
142
143        <activity
143-->[:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
144            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
144-->[:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
145            android:exported="false"
145-->[:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
146            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
146-->[:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
147
148        <service
148-->[:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
149            android:name="com.google.firebase.components.ComponentDiscoveryService"
149-->[:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
150            android:directBootAware="true"
150-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
151            android:exported="false" >
151-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:14:13-37
152            <meta-data
152-->[:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
153                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
153-->[:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
155            <meta-data
155-->[:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
156                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
156-->[:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
158            <meta-data
158-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:15:13-17:85
159                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
159-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:16:17-126
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:17:17-82
161            <meta-data
161-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:18:13-20:85
162                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
162-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:19:17-115
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:20:17-82
164            <meta-data
164-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:29:13-31:85
165                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
165-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:30:17-117
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:31:17-82
167            <meta-data
167-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
168                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
168-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
170            <meta-data
170-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
171                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
171-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
173            <meta-data
173-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
174                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
174-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
176            <meta-data
176-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
177                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
177-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
179            <meta-data
179-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
180                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
180-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
182        </service>
183        <service
183-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:22:9-25:40
184            android:name="com.google.firebase.sessions.SessionLifecycleService"
184-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:23:13-80
185            android:enabled="true"
185-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:24:13-35
186            android:exported="false" />
186-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:25:13-37
187
188        <provider
188-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
189            android:name="com.google.firebase.provider.FirebaseInitProvider"
189-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
190            android:authorities="br.com.unimedfortaleza.pa_tablet.firebaseinitprovider"
190-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
191            android:directBootAware="true"
191-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
192            android:exported="false"
192-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
193            android:initOrder="100" />
193-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
194        <provider
194-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
195            android:name="androidx.startup.InitializationProvider"
195-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
196            android:authorities="br.com.unimedfortaleza.pa_tablet.androidx-startup"
196-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
197            android:exported="false" >
197-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
198            <meta-data
198-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
199                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
199-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
200                android:value="androidx.startup" />
200-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
201            <meta-data
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
202                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
203                android:value="androidx.startup" />
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
204        </provider>
205
206        <uses-library
206-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
207            android:name="androidx.window.extensions"
207-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
208            android:required="false" />
208-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
209        <uses-library
209-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
210            android:name="androidx.window.sidecar"
210-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
211            android:required="false" />
211-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
212
213        <meta-data
213-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
214            android:name="com.google.android.gms.version"
214-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
215            android:value="@integer/google_play_services_version" />
215-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
216
217        <service
217-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
218            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
218-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
219            android:exported="false" >
219-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
220            <meta-data
220-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
221                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
221-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
222                android:value="cct" />
222-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
223        </service>
224
225        <receiver
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
226            android:name="androidx.profileinstaller.ProfileInstallReceiver"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
227            android:directBootAware="false"
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
228            android:enabled="true"
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
229            android:exported="true"
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
230            android:permission="android.permission.DUMP" >
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
232                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
233            </intent-filter>
234            <intent-filter>
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
235                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
236            </intent-filter>
237            <intent-filter>
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
238                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
239            </intent-filter>
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
241                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
242            </intent-filter>
243        </receiver>
244
245        <service
245-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
246            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
246-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
247            android:exported="false"
247-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
248            android:permission="android.permission.BIND_JOB_SERVICE" >
248-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
249        </service>
250
251        <receiver
251-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
252            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
252-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
253            android:exported="false" />
253-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
254    </application>
255
256</manifest>
