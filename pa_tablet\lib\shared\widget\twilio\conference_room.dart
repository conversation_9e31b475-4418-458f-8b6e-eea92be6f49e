// import 'dart:async';
// import 'dart:typed_data';

// import 'package:collection/collection.dart' show IterableExtension;
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:pa_tablet/shared/widget/twilio/participant.dart';
// import 'package:twilio_programmable_video/twilio_programmable_video.dart';
// import 'package:uuid/uuid.dart';

// class ConferenceRoom with ChangeNotifier {
//   final String name;
//   final String token;
//   final String identity;

//   final StreamController<bool> _onAudioEnabledStreamController =
//       StreamController<bool>.broadcast();
//   late Stream<bool> onAudioEnabled;
//   final StreamController<bool> _onVideoEnabledStreamController =
//       StreamController<bool>.broadcast();
//   late Stream<bool> onVideoEnabled;
//   late final StreamController<Map<String, bool>> _flashStateStreamController;
//   late Stream<Map<String, bool>> flashStateStream;
//   late final StreamController<Map<String, bool>> _speakerStateStreamController;
//   late Stream<Map<String, bool>> speakerStateStream;
//   final StreamController<Exception> _onExceptionStreamController =
//       StreamController<Exception>.broadcast();
//   late Stream<Exception> onException;
//   final StreamController<NetworkQualityLevel>
//       _onNetworkQualityStreamController =
//       StreamController<NetworkQualityLevel>.broadcast();
//   late Stream<NetworkQualityLevel> onNetworkQualityLevel;

//   final Completer<Room> _completer = Completer<Room>();

//   final List<ParticipantWidget> _participants = [];
//   final List<ParticipantBuffer> _participantBuffer = [];
//   final List<StreamSubscription> _streamSubscriptions = [];
//   final List<RemoteDataTrack> _dataTracks = [];
//   final List<String> _messages = [];

//   late CameraCapturer _cameraCapturer;
//   late Room _room;
//   late Timer _timer;

//   bool flashEnabled = false;
//   bool speakerphoneEnabled = true;
//   bool bluetoothPreferred = true;
//   var trackId;

//   ConferenceRoom({
//     required this.name,
//     required this.token,
//     required this.identity,
//   }) {
//     onAudioEnabled = _onAudioEnabledStreamController.stream;
//     onVideoEnabled = _onVideoEnabledStreamController.stream;
//     _flashStateStreamController =
//         StreamController<Map<String, bool>>.broadcast(onListen: () {
//       _updateFlashState();
//     });
//     _speakerStateStreamController =
//         StreamController<Map<String, bool>>.broadcast(onListen: () {
//       _updateSpeakerState();
//     });
//     flashStateStream = _flashStateStreamController.stream;
//     speakerStateStream = _speakerStateStreamController.stream;
//     onException = _onExceptionStreamController.stream;
//     onNetworkQualityLevel = _onNetworkQualityStreamController.stream;
//   }

//   List<ParticipantWidget> get participants {
//     return [..._participants];
//   }

//   Future<Room> connect() async {
//     debugPrint('ConferenceRoom.connect()');
//     try {
//       await TwilioProgrammableVideo.debug(
//           dart: true, native: true, audio: true);
//       _streamSubscriptions
//           .add(TwilioProgrammableVideo.onAudioNotification.listen((event) {
//         debugPrint('ConferenceRoom::onAudioNotificationEvent => $event');
//       }));
//       await TwilioProgrammableVideo.setAudioSettings(
//           speakerphoneEnabled: speakerphoneEnabled,
//           bluetoothPreferred: bluetoothPreferred);

//       final sources = await CameraSource.getSources();
//       _cameraCapturer = CameraCapturer(
//         sources.firstWhere((source) => source.isFrontFacing),
//       );
//       trackId = const Uuid().v4();

//       var connectOptions = ConnectOptions(
//         token,
//         roomName: name,
//         preferredAudioCodecs: [OpusCodec()],
//         audioTracks: [LocalAudioTrack(true, 'audio_track-$trackId')],
//         dataTracks: [
//           LocalDataTrack(
//             DataTrackOptions(name: 'data_track-$trackId'),
//           )
//         ],
//         videoTracks: [LocalVideoTrack(true, _cameraCapturer)],
//         enableNetworkQuality: true,
//         networkQualityConfiguration: NetworkQualityConfiguration(
//           remote: NetworkQualityVerbosity.NETWORK_QUALITY_VERBOSITY_MINIMAL,
//         ),
//         enableDominantSpeaker: true,
//       );

//       _room = await TwilioProgrammableVideo.connect(connectOptions);

//       _streamSubscriptions.add(_room.onConnected.listen(_onConnected));
//       _streamSubscriptions.add(_room.onDisconnected.listen(_onDisconnected));
//       _streamSubscriptions.add(_room.onReconnecting.listen(_onReconnecting));
//       _streamSubscriptions
//           .add(_room.onConnectFailure.listen(_onConnectFailure));
//       _streamSubscriptions
//           .add(_cameraCapturer.onCameraSwitched!.listen(_onCameraSwitched));

//       await _updateFlashState();
//       await _updateSpeakerState();

//       return _completer.future;
//     } catch (err) {
//       debugPrint(err.toString());
//       rethrow;
//     }
//   }

//   Future<void> disconnect() async {
//     debugPrint('ConferenceRoom.disconnect()');
//     _timer.cancel();
//     await TwilioProgrammableVideo.disableAudioSettings();
//     await _room.disconnect();
//   }

//   @override
//   void dispose() {
//     debugPrint('ConferenceRoom.dispose()');
//     _disposeStreamsAndSubscriptions();
//     super.dispose();
//   }

//   Future<void> _disposeStreamsAndSubscriptions() async {
//     await _onAudioEnabledStreamController.close();
//     await _onVideoEnabledStreamController.close();
//     await _flashStateStreamController.close();
//     await _speakerStateStreamController.close();
//     await _onExceptionStreamController.close();
//     await _onNetworkQualityStreamController.close();
//     for (var streamSubscription in _streamSubscriptions) {
//       await streamSubscription.cancel();
//     }
//   }

//   Future<List<StatsReport>?> getStats() async {
//     return await TwilioProgrammableVideo.getStats();
//   }

//   Future<void> sendMessage(String message) async {
//     final tracks = _room.localParticipant?.localDataTracks ?? [];
//     final localDataTrack = tracks.isEmpty ? null : tracks[0].localDataTrack;
//     if (localDataTrack == null || _messages.isNotEmpty) {
//       debugPrint(
//           'ConferenceRoom.sendMessage => Track is not available yet, buffering message.');
//       _messages.add(message);
//       return;
//     }
//     await localDataTrack.send(message);
//   }

//   Future<void> sendBufferMessage(ByteBuffer message) async {
//     final tracks = _room.localParticipant?.localDataTracks ?? [];
//     final localDataTrack = tracks.isEmpty ? null : tracks[0].localDataTrack;
//     if (localDataTrack == null) {
//       return;
//     }
//     await localDataTrack.sendBuffer(message);
//   }

//   Future<void> toggleVideoEnabled() async {
//     final tracks = _room.localParticipant?.localVideoTracks ?? [];
//     final localVideoTrack = tracks.isEmpty ? null : tracks[0].localVideoTrack;
//     if (localVideoTrack == null) {
//       debugPrint(
//           'ConferenceRoom.toggleVideoEnabled() => Track is not available yet!');
//       return;
//     }
//     await localVideoTrack.enable(!localVideoTrack.isEnabled);

//     var index = _participants
//         .indexWhere((ParticipantWidget participant) => !participant.isRemote);
//     if (index < 0) {
//       return;
//     }
//     _participants[index] =
//         _participants[index].copyWith(videoEnabled: localVideoTrack.isEnabled);
//     debugPrint(
//         'ConferenceRoom.toggleVideoEnabled() => ${localVideoTrack.isEnabled}');
//     _onVideoEnabledStreamController.add(localVideoTrack.isEnabled);
//     notifyListeners();
//   }

//   Future<void> toggleMute(RemoteParticipant remoteParticipant) async {
//     final enabled = await remoteParticipant
//         .remoteAudioTracks.first.remoteAudioTrack!
//         .isPlaybackEnabled();
//     remoteParticipant.remoteAudioTracks
//         .forEach((remoteAudioTrackPublication) async {
//       final remoteAudioTrack = remoteAudioTrackPublication.remoteAudioTrack;
//       if (remoteAudioTrack != null && enabled != null) {
//         await remoteAudioTrack.enablePlayback(!enabled);
//       }
//     });

//     var index = _participants.indexWhere((ParticipantWidget participant) =>
//         participant.id == remoteParticipant.sid);
//     if (index < 0) {
//       return;
//     }
//     _participants[index] =
//         _participants[index].copyWith(audioEnabledLocally: !enabled!);
//     notifyListeners();
//   }

//   Future<void> toggleAudioEnabled() async {
//     final tracks = _room.localParticipant?.localAudioTracks ?? [];
//     final localAudioTrack = tracks.isEmpty ? null : tracks[0].localAudioTrack;
//     if (localAudioTrack == null) {
//       debugPrint(
//           'ConferenceRoom.toggleAudioEnabled() => Track is not available yet!');
//       return;
//     }
//     await localAudioTrack.enable(!localAudioTrack.isEnabled);

//     var index = _participants
//         .indexWhere((ParticipantWidget participant) => !participant.isRemote);
//     if (index < 0) {
//       return;
//     }
//     _participants[index] =
//         _participants[index].copyWith(audioEnabled: localAudioTrack.isEnabled);
//     debugPrint(
//         'ConferenceRoom.toggleAudioEnabled() => ${localAudioTrack.isEnabled}');
//     _onAudioEnabledStreamController.add(localAudioTrack.isEnabled);
//     notifyListeners();
//   }

//   Future<void> switchCamera() async {
//     debugPrint('ConferenceRoom.switchCamera()');
//     final sources = await CameraSource.getSources();
//     final source = sources.firstWhere((source) {
//       if (_cameraCapturer.source!.isFrontFacing) {
//         return source.isBackFacing;
//       }
//       return source.isFrontFacing;
//     });

//     await _cameraCapturer.switchCamera(source);
//   }

//   Future<void> toggleSpeaker() async {
//     debugPrint('ConferenceRoom.toggleSpeaker()');

//     speakerphoneEnabled = !speakerphoneEnabled;

//     await TwilioProgrammableVideo.setAudioSettings(
//       speakerphoneEnabled: speakerphoneEnabled,
//       bluetoothPreferred: bluetoothPreferred,
//     );

//     await _updateSpeakerState();
//   }

//   Future<void> toggleFlashlight() async {
//     await _cameraCapturer.setTorch(!flashEnabled);
//     flashEnabled = !flashEnabled;
//     await _updateFlashState();
//   }

//   void addDummy({required Widget child}) {
//     debugPrint('ConferenceRoom.addDummy()');
//     if (_participants.length >= 18) {
//       throw PlatformException(
//         code: 'ConferenceRoom.maximumReached',
//         message: 'Maximum reached',
//         details:
//             'Currently the lay-out can only render a maximum of 18 participants',
//       );
//     }
//     _participants.insert(
//       0,
//       ParticipantWidget(
//         id: (_participants.length + 1).toString(),
//         isRemote: true,
//         audioEnabled: true,
//         videoEnabled: true,
//         isDummy: true,
//         child: child,
//       ),
//     );
//     notifyListeners();
//   }

//   void removeDummy() {
//     debugPrint('ConferenceRoom.removeDummy()');
//     var dummy =
//         _participants.firstWhereOrNull((participant) => participant.isDummy);
//     if (dummy != null) {
//       _participants.remove(dummy);
//       notifyListeners();
//     }
//   }

//   void _onDisconnected(RoomDisconnectedEvent event) {
//     debugPrint('ConferenceRoom._onDisconnected');
//     _timer.cancel();
//   }

//   void _onReconnecting(RoomReconnectingEvent room) {
//     debugPrint('ConferenceRoom._onReconnecting');
//     _timer.cancel();
//   }

//   void _onConnected(Room room) {
//     debugPrint('ConferenceRoom._onConnected => state: ${room.state}');

//     // When connected for the first time, add remote participant listeners
//     _streamSubscriptions
//         .add(_room.onParticipantConnected.listen(_onParticipantConnected));
//     _streamSubscriptions.add(
//         _room.onParticipantDisconnected.listen(_onParticipantDisconnected));
//     _streamSubscriptions
//         .add(_room.onDominantSpeakerChange.listen(_onDominantSpeakerChanged));

//     final localParticipant = room.localParticipant;
//     if (localParticipant == null) {
//       debugPrint('ConferenceRoom._onConnected => localParticipant is null');
//       return;
//     }

//     // Only add ourselves when connected for the first time too.
//     _participants.add(
//       _buildParticipant(
//           child: localParticipant.localVideoTracks[0].localVideoTrack.widget(),
//           id: identity,
//           audioEnabled: true,
//           videoEnabled: true,
//           networkQualityLevel: localParticipant.networkQualityLevel,
//           onNetworkQualityChanged:
//               localParticipant.onNetworkQualityLevelChanged),
//     );

//     for (final remoteParticipant in room.remoteParticipants) {
//       var participant = _participants.firstWhereOrNull(
//           (participant) => participant.id == remoteParticipant.sid);
//       if (participant == null) {
//         debugPrint(
//             'Adding participant that was already present in the room ${remoteParticipant.sid}, before I connected');
//         _addRemoteParticipantListeners(remoteParticipant);
//       }
//     }

//     // We have to listen for the [onDataTrackPublished] event on the [LocalParticipant] in
//     // order to be able to use the [send] method.
//     _streamSubscriptions.add(localParticipant.onDataTrackPublished
//         .listen(_onLocalDataTrackPublished));
//     notifyListeners();
//     _completer.complete(room);

//     _timer = Timer.periodic(const Duration(minutes: 1), (_) {
//       // Let's see if we can send some data over the DataTrack API
//       sendMessage('And another minute has passed since I connected...');
//       // Also try the ByteBuffer way of sending data
//       final list =
//           'This data has been sent over the ByteBuffer channel of the DataTrack API'
//               .codeUnits;
//       var bytes = Uint8List.fromList(list);
//       sendBufferMessage(bytes.buffer);
//     });
//   }

//   void _onLocalDataTrackPublished(LocalDataTrackPublishedEvent event) {
//     // Send buffered messages, if any...
//     while (_messages.isNotEmpty) {
//       var message = _messages.removeAt(0);
//       debugPrint('Sending buffered message: $message');
//       event.localDataTrackPublication.localDataTrack!.send(message);
//     }
//   }

//   void _onConnectFailure(RoomConnectFailureEvent event) {
//     debugPrint('ConferenceRoom._onConnectFailure: ${event.exception}');
//     _completer.completeError(event.exception ??
//         const TwilioException(TwilioException.unknownException,
//             'An unknown connection failure occurred.'));
//   }

//   void _onDominantSpeakerChanged(DominantSpeakerChangedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDominantSpeakerChanged: ${event.remoteParticipant?.identity}');
//     var oldDominantParticipantIndex =
//         _participants.indexWhere((p) => p.isDominant);
//     if (oldDominantParticipantIndex >= 0) {
//       _participants[oldDominantParticipantIndex] =
//           _participants[oldDominantParticipantIndex]
//               .copyWith(isDominant: false);
//     }

//     var newDominantParticipantIndex =
//         _participants.indexWhere((p) => p.id == event.remoteParticipant?.sid);
//     _participants[newDominantParticipantIndex] =
//         _participants[newDominantParticipantIndex].copyWith(isDominant: true);
//     notifyListeners();
//   }

//   void _onParticipantConnected(RoomParticipantConnectedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onParticipantConnected, ${event.remoteParticipant.sid}');
//     _addRemoteParticipantListeners(event.remoteParticipant);
//   }

//   void _onParticipantDisconnected(RoomParticipantDisconnectedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onParticipantDisconnected: ${event.remoteParticipant.sid}');
//     _participants.removeWhere(
//         (ParticipantWidget p) => p.id == event.remoteParticipant.sid);
//     notifyListeners();
//   }

//   Future _onCameraSwitched(CameraSwitchedEvent event) async {
//     flashEnabled = false;
//     await _updateFlashState();
//   }

//   Future _updateFlashState() async {
//     var flashState = <String, bool>{
//       'hasFlash': _cameraCapturer.hasTorch,
//       'flashEnabled': flashEnabled,
//     };
//     _flashStateStreamController.add(flashState);
//   }

//   Future _updateSpeakerState() async {
//     var speakerState = <String, bool>{
//       'speakerOn': speakerphoneEnabled,
//     };
//     _speakerStateStreamController.add(speakerState);
//   }

//   ParticipantWidget _buildParticipant({
//     required Widget child,
//     required String? id,
//     required bool audioEnabled,
//     required bool videoEnabled,
//     required NetworkQualityLevel networkQualityLevel,
//     required Stream<NetworkQualityLevelChangedEvent> onNetworkQualityChanged,
//     RemoteParticipant? remoteParticipant,
//   }) {
//     return ParticipantWidget(
//       id: remoteParticipant?.sid,
//       isRemote: remoteParticipant != null,
//       audioEnabled: audioEnabled,
//       videoEnabled: videoEnabled,
//       networkQualityLevel: networkQualityLevel,
//       onNetworkQualityChanged: onNetworkQualityChanged,
//       toggleMute: () => toggleMute(remoteParticipant!),
//       child: child,
//     );
//   }

//   void _addRemoteParticipantListeners(RemoteParticipant remoteParticipant) {
//     debugPrint(
//         'ConferenceRoom._addRemoteParticipantListeners() => Adding listeners to remoteParticipant ${remoteParticipant.sid}');
//     _streamSubscriptions.add(
//         remoteParticipant.onAudioTrackDisabled.listen(_onAudioTrackDisabled));
//     _streamSubscriptions.add(
//         remoteParticipant.onAudioTrackEnabled.listen(_onAudioTrackEnabled));
//     _streamSubscriptions.add(
//         remoteParticipant.onAudioTrackPublished.listen(_onAudioTrackPublished));
//     _streamSubscriptions.add(remoteParticipant.onAudioTrackSubscribed
//         .listen(_onAudioTrackSubscribed));
//     _streamSubscriptions.add(remoteParticipant.onAudioTrackSubscriptionFailed
//         .listen(_onAudioTrackSubscriptionFailed));
//     _streamSubscriptions.add(remoteParticipant.onAudioTrackUnpublished
//         .listen(_onAudioTrackUnpublished));
//     _streamSubscriptions.add(remoteParticipant.onAudioTrackUnsubscribed
//         .listen(_onAudioTrackUnsubscribed));

//     _streamSubscriptions.add(
//         remoteParticipant.onDataTrackPublished.listen(_onDataTrackPublished));
//     _streamSubscriptions.add(
//         remoteParticipant.onDataTrackSubscribed.listen(_onDataTrackSubscribed));
//     _streamSubscriptions.add(remoteParticipant.onDataTrackSubscriptionFailed
//         .listen(_onDataTrackSubscriptionFailed));
//     _streamSubscriptions.add(remoteParticipant.onDataTrackUnpublished
//         .listen(_onDataTrackUnpublished));
//     _streamSubscriptions.add(remoteParticipant.onDataTrackUnsubscribed
//         .listen(_onDataTrackUnsubscribed));

//     _streamSubscriptions.add(remoteParticipant.onNetworkQualityLevelChanged
//         .listen(_onNetworkQualityChanged));

//     _streamSubscriptions.add(
//         remoteParticipant.onVideoTrackDisabled.listen(_onVideoTrackDisabled));
//     _streamSubscriptions.add(
//         remoteParticipant.onVideoTrackEnabled.listen(_onVideoTrackEnabled));
//     _streamSubscriptions.add(
//         remoteParticipant.onVideoTrackPublished.listen(_onVideoTrackPublished));
//     _streamSubscriptions.add(remoteParticipant.onVideoTrackSubscribed
//         .listen(_onVideoTrackSubscribed));
//     _streamSubscriptions.add(remoteParticipant.onVideoTrackSubscriptionFailed
//         .listen(_onVideoTrackSubscriptionFailed));
//     _streamSubscriptions.add(remoteParticipant.onVideoTrackUnpublished
//         .listen(_onVideoTrackUnpublished));
//     _streamSubscriptions.add(remoteParticipant.onVideoTrackUnsubscribed
//         .listen(_onVideoTrackUnsubscribed));
//   }

//   void _onAudioTrackDisabled(RemoteAudioTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackDisabled(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrackPublication.trackSid}, isEnabled: ${event.remoteAudioTrackPublication.isTrackEnabled}');
//     _setRemoteAudioEnabled(event);
//   }

//   void _onAudioTrackEnabled(RemoteAudioTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackEnabled(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrackPublication.trackSid}, isEnabled: ${event.remoteAudioTrackPublication.isTrackEnabled}');
//     _setRemoteAudioEnabled(event);
//   }

//   void _onAudioTrackPublished(RemoteAudioTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackPublished(), ${event.remoteParticipant.sid}}');
//   }

//   void _onAudioTrackSubscribed(RemoteAudioTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackSubscribed(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrackPublication.trackSid}');
//     _addOrUpdateParticipant(event);
//   }

//   void _onAudioTrackSubscriptionFailed(
//       RemoteAudioTrackSubscriptionFailedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackSubscriptionFailed(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrackPublication.trackSid}');
//     _onExceptionStreamController.add(
//       PlatformException(
//         code: 'ConferenceRoom.audioTrackSubscriptionFailed',
//         message: 'AudioTrack Subscription Failed',
//         details: event.exception.toString(),
//       ),
//     );
//   }

//   void _onAudioTrackUnpublished(RemoteAudioTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackUnpublished(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrackPublication.trackSid}');
//   }

//   void _onAudioTrackUnsubscribed(RemoteAudioTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onAudioTrackUnsubscribed(), ${event.remoteParticipant.sid}, ${event.remoteAudioTrack.sid}');
//   }

//   void _onDataTrackPublished(RemoteDataTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDataTrackPublished(), ${event.remoteParticipant.sid}}');
//   }

//   void _onDataTrackSubscribed(RemoteDataTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDataTrackSubscribed(), ${event.remoteParticipant.sid}, ${event.remoteDataTrackPublication.trackSid}');
//     final dataTrack = event.remoteDataTrackPublication.remoteDataTrack;
//     if (dataTrack == null) {
//       debugPrint(
//           'ConferenceRoom._onDataTrackSubscribed() => dataTrack == null');
//       return;
//     }
//     _dataTracks.add(dataTrack);
//     _streamSubscriptions.add(dataTrack.onMessage.listen(_onMessage));
//     _streamSubscriptions
//         .add(dataTrack.onBufferMessage.listen(_onBufferMessage));
//   }

//   void _onDataTrackSubscriptionFailed(
//       RemoteDataTrackSubscriptionFailedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDataTrackSubscriptionFailed(), ${event.remoteParticipant.sid}, ${event.remoteDataTrackPublication.trackSid}');
//     _onExceptionStreamController.add(
//       PlatformException(
//         code: 'ConferenceRoom.dataTrackSubscriptionFailed',
//         message: 'DataTrack Subscription Failed',
//         details: event.exception.toString(),
//       ),
//     );
//   }

//   void _onDataTrackUnpublished(RemoteDataTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDataTrackUnpublished(), ${event.remoteParticipant.sid}, ${event.remoteDataTrackPublication.trackSid}');
//   }

//   void _onDataTrackUnsubscribed(RemoteDataTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onDataTrackUnsubscribed(), ${event.remoteParticipant.sid}, ${event.remoteDataTrack.sid}');
//   }

//   void _onNetworkQualityChanged(RemoteNetworkQualityLevelChangedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onNetworkQualityChanged(), ${event.remoteParticipant.sid}, ${event.networkQualityLevel}');
//   }

//   void _onVideoTrackDisabled(RemoteVideoTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackDisabled(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrackPublication.trackSid}, isEnabled: ${event.remoteVideoTrackPublication.isTrackEnabled}');
//     _setRemoteVideoEnabled(event);
//   }

//   void _onVideoTrackEnabled(RemoteVideoTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackEnabled(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrackPublication.trackSid}, isEnabled: ${event.remoteVideoTrackPublication.isTrackEnabled}');
//     _setRemoteVideoEnabled(event);
//   }

//   void _onVideoTrackPublished(RemoteVideoTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackPublished(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrackPublication.trackSid}');
//   }

//   void _onVideoTrackSubscribed(RemoteVideoTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackSubscribed(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrack.sid}');
//     _addOrUpdateParticipant(event);
//   }

//   void _onVideoTrackSubscriptionFailed(
//       RemoteVideoTrackSubscriptionFailedEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackSubscriptionFailed(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrackPublication.trackSid}');
//     _onExceptionStreamController.add(
//       PlatformException(
//         code: 'ConferenceRoom.videoTrackSubscriptionFailed',
//         message: 'VideoTrack Subscription Failed',
//         details: event.exception.toString(),
//       ),
//     );
//   }

//   void _onVideoTrackUnpublished(RemoteVideoTrackEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackUnpublished(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrackPublication.trackSid}');
//   }

//   void _onVideoTrackUnsubscribed(RemoteVideoTrackSubscriptionEvent event) {
//     debugPrint(
//         'ConferenceRoom._onVideoTrackUnsubscribed(), ${event.remoteParticipant.sid}, ${event.remoteVideoTrack.sid}');
//   }

//   void _onMessage(RemoteDataTrackStringMessageEvent event) {
//     debugPrint('onMessage => ${event.remoteDataTrack.sid}, ${event.message}');
//   }

//   void _onBufferMessage(RemoteDataTrackBufferMessageEvent event) {
//     debugPrint(
//         'onBufferMessage => ${event.remoteDataTrack.sid}, ${String.fromCharCodes(event.message.asUint8List())}');
//   }

//   void _setRemoteAudioEnabled(RemoteAudioTrackEvent event) {
//     var index = _participants.indexWhere((ParticipantWidget participant) =>
//         participant.id == event.remoteParticipant.sid);
//     if (index < 0) {
//       return;
//     }
//     _participants[index] = _participants[index].copyWith(
//         audioEnabled: event.remoteAudioTrackPublication.isTrackEnabled);
//     notifyListeners();
//   }

//   void _setRemoteVideoEnabled(RemoteVideoTrackEvent event) {
//     var index = _participants.indexWhere((ParticipantWidget participant) =>
//         participant.id == event.remoteParticipant.sid);
//     if (index < 0) {
//       return;
//     }
//     _participants[index] = _participants[index].copyWith(
//         videoEnabled: event.remoteVideoTrackPublication.isTrackEnabled);
//     notifyListeners();
//   }

//   void _addOrUpdateParticipant(RemoteParticipantEvent event) {
//     debugPrint(
//         'ConferenceRoom._addOrUpdateParticipant(), ${event.remoteParticipant.sid}');
//     final participant = _participants.firstWhereOrNull(
//       (ParticipantWidget participant) =>
//           participant.id == event.remoteParticipant.sid,
//     );
//     if (participant != null) {
//       debugPrint(
//           'Participant found: ${participant.id}, updating A/V enabled values');
//       if (event is RemoteVideoTrackEvent) {
//         _setRemoteVideoEnabled(event);
//       } else if (event is RemoteAudioTrackEvent) {
//         _setRemoteAudioEnabled(event);
//       }
//     } else {
//       final bufferedParticipant = _participantBuffer.firstWhereOrNull(
//         (ParticipantBuffer participant) =>
//             participant.id == event.remoteParticipant.sid,
//       );
//       if (bufferedParticipant != null) {
//         _participantBuffer.remove(bufferedParticipant);
//       } else if (event is RemoteAudioTrackEvent) {
//         debugPrint(
//             'Audio subscription came first, waiting for the video subscription...');
//         _participantBuffer.add(
//           ParticipantBuffer(
//             id: event.remoteParticipant.sid,
//             audioEnabled:
//                 event.remoteAudioTrackPublication.remoteAudioTrack?.isEnabled ??
//                     true,
//           ),
//         );
//         return;
//       }
//       if (event is RemoteVideoTrackSubscriptionEvent) {
//         debugPrint('New participant, adding: ${event.remoteParticipant.sid}');
//         _participants.insert(
//           0,
//           _buildParticipant(
//             child: event.remoteVideoTrack.widget(),
//             id: event.remoteParticipant.sid,
//             remoteParticipant: event.remoteParticipant,
//             audioEnabled: bufferedParticipant?.audioEnabled ?? true,
//             videoEnabled:
//                 event.remoteVideoTrackPublication.remoteVideoTrack?.isEnabled ??
//                     true,
//             networkQualityLevel: event.remoteParticipant.networkQualityLevel,
//             onNetworkQualityChanged:
//                 event.remoteParticipant.onNetworkQualityLevelChanged,
//           ),
//         );
//       }
//       notifyListeners();
//     }
//   }
// }
