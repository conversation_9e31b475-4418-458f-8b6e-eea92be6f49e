export const icons = {
  chatSend: require('./chat-send.png'),
  defaultAvatar: require('./default-avatar.png'),
  locked: require('./locked.png'),
  more: require('./more.png'),
  mute: require('./mute.png'),
  muted: require('./muted.png'),
  shareOn: require('./share-on.png'),
  shareOff: require('./share-off.png'),
  speakerOn: require('./speaker-on.png'),
  speakerOff: require('./speaker-off.png'),
  switchCamera: require('./switch-camera.png'),
  hamburger: require('./hamburger.png'),
  questionBalloon: require('./question-ballon.png'),
  talking: require('./talking.png'),
  unmute: require('./unmute.png'),
  unlocked: require('./unlocked.png'),
  videoOn: require('./video-on.png'),
  videoOff: require('./video-off.png'),
};

export type IconTypes = keyof typeof icons;
