biometria_perfilapps
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-67113bb86da11c324a65a00281e9763436a5bb87/biometria_perfilapps/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-67113bb86da11c324a65a00281e9763436a5bb87/biometria_perfilapps/lib/
url_launcher
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7abc1bf67a8088286319e3f7a8a5406195a2fa34/url_launcher/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7abc1bf67a8088286319e3f7a8a5406195a2fa34/url_launcher/lib/
url_launcher_ios
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7abc1bf67a8088286319e3f7a8a5406195a2fa34/url_launcher_ios/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7abc1bf67a8088286319e3f7a8a5406195a2fa34/url_launcher_ios/lib/
http_client
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7c7fbbba6ec19b4f1efb3284e842b9a0392b54f0/http_client/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7c7fbbba6ec19b4f1efb3284e842b9a0392b54f0/http_client/lib/
remote_log_elastic
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7c7fbbba6ec19b4f1efb3284e842b9a0392b54f0/remote_log_elastic/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-7c7fbbba6ec19b4f1efb3284e842b9a0392b54f0/remote_log_elastic/lib/
header_login
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/header_login/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/header_login/lib/
unimed_select
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/unimed_select/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/unimed_select/lib/
websocket_service
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/websocket_service/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter_packages-e321ff8bc495511ce3962ed896f80a99223f147d/websocket_service/lib/
_flutterfire_internals
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.35/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.35/lib/
adaptive_number
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/adaptive_number-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/adaptive_number-1.0.0/lib/
after_layout
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/after_layout-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/after_layout-1.2.0/lib/
archive
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-4.0.7/lib/
args
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.13.0/lib/
auto_size_text
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
bloc
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bloc-7.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bloc-7.2.1/lib/
boolean_selector
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
cached_network_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1/lib/
cached_network_image_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/
cached_network_image_web
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1/lib/
camera
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera-0.10.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera-0.10.6/lib/
camera_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_android-0.10.10+3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_android-0.10.10+3/lib/
camera_avfoundation
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.20+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_avfoundation-0.9.20+1/lib/
camera_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_platform_interface-2.10.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/
camera_web
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_web-0.3.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/camera_web-0.3.5/lib/
characters
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity-3.0.6/lib/
connectivity_for_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_for_web-0.4.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_for_web-0.4.0+1/lib/
connectivity_macos
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_macos-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_macos-0.2.1+2/lib/
connectivity_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_platform_interface-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_platform_interface-2.0.1/lib/
connectivity_plus
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-6.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/
connectivity_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/
convert
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2/lib/
cross_file
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
dart_jsonwebtoken
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_jsonwebtoken-2.17.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_jsonwebtoken-2.17.0/lib/
dartlin
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dartlin-0.6.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dartlin-0.6.3/lib/
dbus
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11/lib/
device_info_plus
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-10.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-10.1.2/lib/
device_info_plus_platform_interface
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/lib/
ed25519_edwards
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ed25519_edwards-0.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ed25519_edwards-0.3.1/lib/
enum_to_string
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/enum_to_string-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/enum_to_string-2.2.1/lib/
equatable
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7/lib/
events_emitter
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/events_emitter-0.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/events_emitter-0.6.0/lib/
extension
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/extension-0.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/extension-0.6.0/lib/
fake_async
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_core
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.32.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.32.0/lib/
firebase_core_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0/lib/
firebase_core_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.23.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.23.0/lib/
firebase_crashlytics
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics-3.5.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics-3.5.7/lib/
firebase_crashlytics_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.6.35/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.6.35/lib/
fixnum
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_background_service
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service-5.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service-5.1.0/lib/
flutter_background_service_android
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_android-6.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_android-6.3.0/lib/
flutter_background_service_ios
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/lib/
flutter_background_service_platform_interface
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_platform_interface-5.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background_service_platform_interface-5.1.2/lib/
flutter_bloc
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_bloc-7.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_bloc-7.3.3/lib/
flutter_cache_manager
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_hooks
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_hooks-0.20.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_hooks-0.20.5/lib/
flutter_i18n
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_i18n-0.36.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_i18n-0.36.3/lib/
flutter_image_compress
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress-2.4.0/lib/
flutter_image_compress_common
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/lib/
flutter_image_compress_macos
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_macos-1.0.3/lib/
flutter_image_compress_ohos
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_ohos-0.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_ohos-0.0.3/lib/
flutter_image_compress_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_platform_interface-1.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_platform_interface-1.0.5/lib/
flutter_image_compress_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_image_compress_web-0.1.5/lib/
flutter_lints
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.3/lib/
flutter_local_notifications
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-18.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications-18.0.1/lib/
flutter_local_notifications_linux
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/lib/
flutter_local_notifications_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_spinkit
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_spinkit-5.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_spinkit-5.2.1/lib/
get_it
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get_it-7.7.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get_it-7.7.0/lib/
google_fonts
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-6.1.0/lib/
gql
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql-1.0.1-alpha+1730759315362/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql-1.0.1-alpha+1730759315362/lib/
gql_dedupe_link
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_dedupe_link-2.0.4-alpha+1715521079596/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_dedupe_link-2.0.4-alpha+1715521079596/lib/
gql_error_link
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_error_link-1.0.0+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_error_link-1.0.0+1/lib/
gql_exec
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_exec-1.1.1-alpha+1699813812660/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_exec-1.1.1-alpha+1699813812660/lib/
gql_http_link
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_http_link-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_http_link-1.1.0/lib/
gql_link
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_link-1.0.1-alpha+1730759315378/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_link-1.0.1-alpha+1730759315378/lib/
gql_transform_link
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_transform_link-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/gql_transform_link-1.0.0/lib/
graphql
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphql-5.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphql-5.2.1/lib/
graphs
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/graphs-2.3.2/lib/
hive
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive-2.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hive-2.2.3/lib/
http
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2/lib/
hydrated_bloc
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hydrated_bloc-7.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hydrated_bloc-7.1.0/lib/
image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4/lib/
image_picker
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib/
image_picker_ios
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
infinite_listview
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/infinite_listview-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/infinite_listview-1.1.0/lib/
intl
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.20.2/lib/
js
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0/lib/
leak_tracker
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.1.1/lib/
logger
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logger-2.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logger-2.6.0/lib/
logging
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0/lib/
mask_text_input_formatter
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mask_text_input_formatter-2.9.0/lib/
matcher
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-1.0.6/lib/
native_device_orientation
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/native_device_orientation-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/native_device_orientation-1.2.1/lib/
nested
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nm-0.5.0/lib/
normalize
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/normalize-0.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/normalize-0.9.1/lib/
numberpicker
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/numberpicker-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/numberpicker-2.1.2/lib/
octo_image
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0/lib/
package_info
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info-2.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info-2.0.2/lib/
package_info_plus
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0/lib/
package_info_plus_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/lib/
path
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
pdfx
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdfx-2.9.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdfx-2.9.2/lib/
permission_handler
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-11.4.0/lib/
permission_handler_android
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-12.1.0/lib/
permission_handler_apple
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0/lib/
photo_view
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/photo_view-0.15.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/photo_view-0.15.0/lib/
platform
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
pointycastle
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointycastle-3.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointycastle-3.9.1/lib/
posix
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/posix-6.0.2/lib/
provider
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5/lib/
rive
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive-0.11.17/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive-0.11.17/lib/
rive_common
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.2.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rive_common-0.2.7/lib/
rxdart
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.28.0/lib/
share
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share-2.0.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/share-2.0.4/lib/
shared_preferences
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
socket_io_client
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_client-1.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_client-1.0.2/lib/
socket_io_common
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_common-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_common-1.0.1/lib/
source_span
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2/lib/
sqflite_android
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.8
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.4.0/lib/
term_glyph
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4/lib/
timezone
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timezone-0.10.1/lib/
toml
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/toml-0.16.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/toml-0.16.0/lib/
twilio_programmable_video_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/twilio_programmable_video_platform_interface-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/twilio_programmable_video_platform_interface-1.1.0/lib/
twilio_programmable_video_web
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/twilio_programmable_video_web-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/twilio_programmable_video_web-1.1.0/lib/
typed_data
3.5
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0/lib/
universal_platform
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_platform-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_platform-1.1.0/lib/
url_launcher_android
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_linux
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
version
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/version-2.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/version-2.0.3/lib/
vibration
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vibration-1.9.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vibration-1.9.0/lib/
vibration_platform_interface
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vibration_platform_interface-0.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vibration_platform_interface-0.0.3/lib/
vm_service
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-15.0.0/lib/
wakelock_plus
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus-1.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus-1.3.2/lib/
wakelock_plus_platform_interface
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3/lib/
web
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-1.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
win32
3.8
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.14.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.14.0/lib/
win32_registry
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-1.1.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-1.1.5/lib/
xdg_directories
3.3
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0/lib/
xml2json
3.7
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml2json-6.2.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml2json-6.2.7/lib/
yaml
3.4
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3/lib/
evaluation
3.5
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/evaluation/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/evaluation/lib/
pa_virtual
2.17
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/pa_virtual/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/pa_virtual/lib/
splash_unimed
2.12
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/splash_unimed/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/splash_unimed/lib/
teleconsulta_unimed
2.17
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/teleconsulta_unimed/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/teleconsulta_unimed/lib/
twilio_programmable_video
2.12
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/twilio_programmable_video/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/flutter_packages/twilio_programmable_video/lib/
pa_tablet
3.0
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/pa-tablet/pa_tablet/
file:///C:/Users/<USER>/OneDrive/Documentos/unimed/pa-tablet/pa_tablet/lib/
sky_engine
3.7
file:///C:/flutter/bin/cache/pkg/sky_engine/
file:///C:/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///C:/flutter/packages/flutter/
file:///C:/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///C:/flutter/packages/flutter_localizations/
file:///C:/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///C:/flutter/packages/flutter_test/
file:///C:/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///C:/flutter/packages/flutter_web_plugins/
file:///C:/flutter/packages/flutter_web_plugins/lib/
2
