-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
MERGED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-32:19
MERGED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:19
MERGED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d58c2205d84cf76c2e7ad7b2acd996ce\transformed\jetified-firebase-measurement-connector-18.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d58c2205d84cf76c2e7ad7b2acd996ce\transformed\jetified-firebase-measurement-connector-18.0.2\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf2dd6c258be64b6a9d7a16837a3e4c3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf2dd6c258be64b6a9d7a16837a3e4c3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:12:5-20
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:12:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4179954667c65251ae87762ced07d1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4179954667c65251ae87762ced07d1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:device_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:pa_virtual] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pa_virtual\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:teleconsulta_unimed] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\teleconsulta_unimed\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:wakelock_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:pdfx] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pdfx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-19:12
MERGED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:connectivity_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:native_device_orientation] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\native_device_orientation\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:rive_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:twilio_programmable_video] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\twilio_programmable_video\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:vibration] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f81cb57f376a4d5dae974045c2758c9\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e453673afc993cf09c7092b0bbe1931e\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91ebcc34ef84995bcffaa812349b3447\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\01919b4ee2955868487f6f8fc2f862df\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d58c2205d84cf76c2e7ad7b2acd996ce\transformed\jetified-firebase-measurement-connector-18.0.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c733cc0c4f56c59443fdc73dfa37add4\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc0aa51b50ded465c0365f098009fab\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f47ee3f633dbdcc773f14fda9d9a511\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff05eca676ffbf9358ec489d462245f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5514a39f6397a447023d02dc3d5a3a1c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3dd86bd936f6046e25bdccd44fed4e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79009c71f19a1968463fdd65c3342240\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54b3d287dbc009d884902f52fa0748d3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f7821d73b5d678f24d7529bacd9bdd8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6d6b851722a5cb8920523cd30ddcaa7\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc3b9f49cd8170b039e96db889c6e72d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78889d27de30b0097f2616f3d2b25464\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb528dd4dd114fd9c96c64b46bbad25f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec2cdd8c45457a3843e9ac3c7b94097\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a37999c5a57511827c78e01689d8480a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\517307daf7cddb3d2eb186bd5297da4c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\45f603ff6d01a7babdd9e44ff1f07c2e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19165038c3cbf9d273fe8fd6160fa1c3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d170c0a192f366892eaa806f41912ab\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\669cc322724e1fbb5435f99135b48fcb\transformed\jetified-datastore-preferences-1.1.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6d0d4e273fb78cb79880ddb2047293\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8776f28a2b282ee4d85498fd244610ef\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf2dd6c258be64b6a9d7a16837a3e4c3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd554a3842102dbdc31b818b39a2d3f9\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69709dc0439b4cc8ac2e692ff0ab5cf2\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\48972fce45c4326718ab888c0fb7fd0e\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\922531bf4ece347df3823a492a23efe8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e2823a4b27d2e5c74ae5df14c325b52\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\664e5628f5c5cf78d437ac3454330dd3\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae9f22eec2dc44e01a0a5828830b8572\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\35513b18b34453943c984ceee038f591\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5dffd6b4215a84163c558d50d7adae4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04ee07e5ed6cc96b53bf7146559de247\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a31c146cf3dc4efcc96a4d357c1102a1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be94d9ee7d78e3d87b248f7a5eba84f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7018c207ab9e28ca443f75fd6d97461d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d59382b24af9c7c2d23e36e9e53288c0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\969b5eab5503b69a04b63899d3c4c419\transformed\jetified-firebase-config-interop-16.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3920fd57d9799cb4c657315b89323f6b\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\170994596e5c07af1c38a4bfd3861629\transformed\heifwriter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c5a29b26d72314ddb6e95c38ae5120\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e1d2ed9424b8ef12cdec8fc21f893d5\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\852e015b6266afb2863e59b783b0e0b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65087825c9cb5a3816c0e760dcaaf8ea\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4179954667c65251ae87762ced07d1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37a5d561d29cc972c06b25a95163067d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd9a9444b4acead0ea876278056c9c0f\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b0549593c94aa8eee6a415782dfca7\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6abf328185cf97b603d60344d45ecec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9364b1ebc75d834a558ae609409b6326\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf8b03cceb2478b7ad92ebd7093c5af5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dbf0bc336ca8773363c93dd9a30c1a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85284ce3c1a09e8533b54c2b0378108\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d84a543c84d7bfdd30cd9231ac92bc42\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e14d118575d90a0a8293dacad53237f0\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:7:5-67
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
MERGED from [:device_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:55
MERGED from [:flutter_image_compress_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:55
MERGED from [:pa_virtual] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pa_virtual\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:pa_virtual] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pa_virtual\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:teleconsulta_unimed] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\teleconsulta_unimed\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:teleconsulta_unimed] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\teleconsulta_unimed\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:wakelock_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\wakelock_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:pdfx] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pdfx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:pdfx] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\pdfx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:native_device_orientation] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\native_device_orientation\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:native_device_orientation] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\native_device_orientation\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\package_info\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:rive_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\rive_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:twilio_programmable_video] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\twilio_programmable_video\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:twilio_programmable_video] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\twilio_programmable_video\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:vibration] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f81cb57f376a4d5dae974045c2758c9\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4f81cb57f376a4d5dae974045c2758c9\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e453673afc993cf09c7092b0bbe1931e\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e453673afc993cf09c7092b0bbe1931e\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91ebcc34ef84995bcffaa812349b3447\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\91ebcc34ef84995bcffaa812349b3447\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\01919b4ee2955868487f6f8fc2f862df\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\01919b4ee2955868487f6f8fc2f862df\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d58c2205d84cf76c2e7ad7b2acd996ce\transformed\jetified-firebase-measurement-connector-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\d58c2205d84cf76c2e7ad7b2acd996ce\transformed\jetified-firebase-measurement-connector-18.0.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c733cc0c4f56c59443fdc73dfa37add4\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c733cc0c4f56c59443fdc73dfa37add4\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc0aa51b50ded465c0365f098009fab\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\abc0aa51b50ded465c0365f098009fab\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f47ee3f633dbdcc773f14fda9d9a511\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1f47ee3f633dbdcc773f14fda9d9a511\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff05eca676ffbf9358ec489d462245f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5ff05eca676ffbf9358ec489d462245f\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5514a39f6397a447023d02dc3d5a3a1c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5514a39f6397a447023d02dc3d5a3a1c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3dd86bd936f6046e25bdccd44fed4e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d3dd86bd936f6046e25bdccd44fed4e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79009c71f19a1968463fdd65c3342240\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79009c71f19a1968463fdd65c3342240\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54b3d287dbc009d884902f52fa0748d3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\54b3d287dbc009d884902f52fa0748d3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f7821d73b5d678f24d7529bacd9bdd8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f7821d73b5d678f24d7529bacd9bdd8\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6d6b851722a5cb8920523cd30ddcaa7\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c6d6b851722a5cb8920523cd30ddcaa7\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc3b9f49cd8170b039e96db889c6e72d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc3b9f49cd8170b039e96db889c6e72d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78889d27de30b0097f2616f3d2b25464\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\78889d27de30b0097f2616f3d2b25464\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb528dd4dd114fd9c96c64b46bbad25f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb528dd4dd114fd9c96c64b46bbad25f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec2cdd8c45457a3843e9ac3c7b94097\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aec2cdd8c45457a3843e9ac3c7b94097\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a37999c5a57511827c78e01689d8480a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a37999c5a57511827c78e01689d8480a\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\517307daf7cddb3d2eb186bd5297da4c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\517307daf7cddb3d2eb186bd5297da4c\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\45f603ff6d01a7babdd9e44ff1f07c2e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\45f603ff6d01a7babdd9e44ff1f07c2e\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19165038c3cbf9d273fe8fd6160fa1c3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19165038c3cbf9d273fe8fd6160fa1c3\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d170c0a192f366892eaa806f41912ab\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d170c0a192f366892eaa806f41912ab\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\669cc322724e1fbb5435f99135b48fcb\transformed\jetified-datastore-preferences-1.1.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.datastore:datastore-preferences:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\669cc322724e1fbb5435f99135b48fcb\transformed\jetified-datastore-preferences-1.1.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6d0d4e273fb78cb79880ddb2047293\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8d6d0d4e273fb78cb79880ddb2047293\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8776f28a2b282ee4d85498fd244610ef\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8776f28a2b282ee4d85498fd244610ef\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf2dd6c258be64b6a9d7a16837a3e4c3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf2dd6c258be64b6a9d7a16837a3e4c3\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd554a3842102dbdc31b818b39a2d3f9\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd554a3842102dbdc31b818b39a2d3f9\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69709dc0439b4cc8ac2e692ff0ab5cf2\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69709dc0439b4cc8ac2e692ff0ab5cf2\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\48972fce45c4326718ab888c0fb7fd0e\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\48972fce45c4326718ab888c0fb7fd0e\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\922531bf4ece347df3823a492a23efe8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\922531bf4ece347df3823a492a23efe8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e2823a4b27d2e5c74ae5df14c325b52\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3e2823a4b27d2e5c74ae5df14c325b52\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\664e5628f5c5cf78d437ac3454330dd3\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\664e5628f5c5cf78d437ac3454330dd3\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae9f22eec2dc44e01a0a5828830b8572\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae9f22eec2dc44e01a0a5828830b8572\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\35513b18b34453943c984ceee038f591\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\35513b18b34453943c984ceee038f591\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5dffd6b4215a84163c558d50d7adae4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5dffd6b4215a84163c558d50d7adae4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04ee07e5ed6cc96b53bf7146559de247\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04ee07e5ed6cc96b53bf7146559de247\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a31c146cf3dc4efcc96a4d357c1102a1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a31c146cf3dc4efcc96a4d357c1102a1\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be94d9ee7d78e3d87b248f7a5eba84f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\be94d9ee7d78e3d87b248f7a5eba84f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7018c207ab9e28ca443f75fd6d97461d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7018c207ab9e28ca443f75fd6d97461d\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d59382b24af9c7c2d23e36e9e53288c0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d59382b24af9c7c2d23e36e9e53288c0\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:5:5-44
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\969b5eab5503b69a04b63899d3c4c419\transformed\jetified-firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\969b5eab5503b69a04b63899d3c4c419\transformed\jetified-firebase-config-interop-16.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3920fd57d9799cb4c657315b89323f6b\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3920fd57d9799cb4c657315b89323f6b\transformed\jetified-firebase-encoders-json-18.0.1\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\170994596e5c07af1c38a4bfd3861629\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\170994596e5c07af1c38a4bfd3861629\transformed\heifwriter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c5a29b26d72314ddb6e95c38ae5120\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\38c5a29b26d72314ddb6e95c38ae5120\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e1d2ed9424b8ef12cdec8fc21f893d5\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e1d2ed9424b8ef12cdec8fc21f893d5\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\852e015b6266afb2863e59b783b0e0b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\852e015b6266afb2863e59b783b0e0b3\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65087825c9cb5a3816c0e760dcaaf8ea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\65087825c9cb5a3816c0e760dcaaf8ea\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4179954667c65251ae87762ced07d1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c4179954667c65251ae87762ced07d1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37a5d561d29cc972c06b25a95163067d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\37a5d561d29cc972c06b25a95163067d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd9a9444b4acead0ea876278056c9c0f\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cd9a9444b4acead0ea876278056c9c0f\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b0549593c94aa8eee6a415782dfca7\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b0549593c94aa8eee6a415782dfca7\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6abf328185cf97b603d60344d45ecec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6abf328185cf97b603d60344d45ecec\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9364b1ebc75d834a558ae609409b6326\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9364b1ebc75d834a558ae609409b6326\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf8b03cceb2478b7ad92ebd7093c5af5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bf8b03cceb2478b7ad92ebd7093c5af5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dbf0bc336ca8773363c93dd9a30c1a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8dbf0bc336ca8773363c93dd9a30c1a6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85284ce3c1a09e8533b54c2b0378108\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85284ce3c1a09e8533b54c2b0378108\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d84a543c84d7bfdd30cd9231ac92bc42\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d84a543c84d7bfdd30cd9231ac92bc42\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e14d118575d90a0a8293dacad53237f0\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\e14d118575d90a0a8293dacad53237f0\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [:flutter_image_compress_common] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_image_compress_common\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-52
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-77
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-65
service#id.flutter.flutter_background_service.BackgroundService
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-16:44
	android:enabled
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-35
	android:exported
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-36
	android:stopWithTask
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-41
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-83
receiver#id.flutter.flutter_background_service.WatchdogReceiver
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-21:39
	android:enabled
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-35
	android:exported
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-82
receiver#id.flutter.flutter_background_service.BootReceiver
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-31:20
	android:enabled
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-78
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-30:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-79
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [:flutter_background_service_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:vibration] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\vibration\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
provider#io.flutter.plugins.share.ShareFileProvider
ADDED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-74
	android:exported
		ADDED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:share] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\share\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-70
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.CAMERA
ADDED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-65
	android:name
		ADDED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-71
	android:name
		ADDED from [:camera_android] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\camera_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-68
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:9:5-79
MERGED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:connectivity] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\connectivity\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:14:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_crashlytics] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_crashlytics\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-130
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\OneDrive\Documentos\unimed\pa-tablet\pa_tablet\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:16:17-126
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a6dcc73efccd483f1334f59c707e79\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:19:17-115
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7641b9f2b81444c74697364073b1f82\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2af4a2ea9b6b17f8389eabe2e0f9fac8\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\83a0543727a504e8a1ad72d706927d64\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\6c3047a89b2eb200b1ea976c9c858768\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b3da58b3b1f8ce04fa099998e9de74f\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c3c191a270235c7af8be1c0e4853b8e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b26b85a27941e0e23b80df88d5132b\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ea97ef9ac24cc7c69efa4c11d6a30483\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#br.com.unimedfortaleza.pa_tablet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#br.com.unimedfortaleza.pa_tablet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\aea6443f4fcedc8ee1e9d511593e1b6d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:8:5-80
	android:name
		ADDED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:8:22-77
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:10:5-76
	android:name
		ADDED from [com.twilio:video-android:7.6.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\990fe7ef7ec42d00ac07336e822b6bbf\transformed\jetified-video-android-7.6.4\AndroidManifest.xml:10:22-73
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\c4db33ddd9746dc185e47a830a427fed\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\f8ab0b8937a1762ca768405ed2d58e73\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6c42da4bd9e84cb9536ff4562787f8a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\ad4fcda9e530cae24234b15ad7bb2c6d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
